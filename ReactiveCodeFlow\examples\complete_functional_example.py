"""
Complete functional programming example demonstrating ReactiveCodeFlow capabilities.

This example shows a real-world data processing pipeline using purely functional
programming paradigms, contrasting with traditional OOP approaches.
"""

import asyncio
from reactivecodeflow.core.types import ExecutionContext, RetryConfig
from reactivecodeflow.core.node import create_node, create_batch_node, execute_node, execute_batch_node
from reactivecodeflow.core.flow import create_flow, add_node, add_transition, execute_flow
from reactivecodeflow.core.async_support import create_async_node, execute_async_node
from expression import Ok, Error
from expression.collections import Map, Seq


def create_data_validation_node():
    """Create a node that validates input data using pure functions."""
    def prep_fn(ctx):
        if ctx.shared_state.contains_key("raw_data"):
            raw_data = ctx.shared_state.get("raw_data")
            return Ok(raw_data)
        else:
            return Error("No raw data provided")
    
    def exec_fn(raw_data):
        """Validate data structure and content."""
        try:
            if not isinstance(raw_data, list):
                return Error("Data must be a list")
            
            if len(raw_data) == 0:
                return Error("Data cannot be empty")
            
            # Validate each item has required fields
            for item in raw_data:
                if not isinstance(item, dict):
                    return Error("Each item must be a dictionary")
                if "id" not in item or "value" not in item:
                    return Error("Each item must have 'id' and 'value' fields")
            
            return Ok(raw_data)
        except Exception as e:
            return Error(f"Validation error: {str(e)}")
    
    def post_fn(ctx, prep_res, exec_res):
        """Store validated data and proceed to transformation."""
        updated_state = ctx.shared_state.add("validated_data", exec_res)
        return Ok(("transform", updated_state))
    
    return create_node("validate", prep_fn, exec_fn, post_fn)


def create_data_transformation_node():
    """Create a node that transforms data using functional composition."""
    def prep_fn(ctx):
        if ctx.shared_state.contains_key("validated_data"):
            validated_data = ctx.shared_state.get("validated_data")
            return Ok(validated_data)
        else:
            return Error("No validated data found")

    def exec_fn(validated_data):
        """Transform all data items using pure functions."""
        try:
            print(f"Processing data: {validated_data}, type: {type(validated_data)}")

            # Transform each item functionally
            transformed_items = []
            for item in validated_data:
                transformed = {
                    "id": item["id"],
                    "original_value": item["value"],
                    "transformed_value": item["value"] * 2 + 10,
                    "category": "high" if item["value"] > 50 else "low",
                    "processed": True
                }
                transformed_items.append(transformed)

            return Ok(transformed_items)
        except Exception as e:
            print(f"Error processing data {validated_data}: {e}")
            return Error(f"Transformation error: {str(e)}")

    def post_fn(ctx, prep_res, exec_res):
        """Aggregate transformed results and compute statistics."""
        results_list = exec_res
        total_items = len(results_list)
        high_category_count = sum(1 for item in results_list if item["category"] == "high")

        statistics = {
            "total_processed": total_items,
            "high_category_count": high_category_count,
            "low_category_count": total_items - high_category_count,
            "processing_rate": high_category_count / total_items if total_items > 0 else 0
        }

        updated_state = (ctx.shared_state
                        .add("transformed_data", results_list)
                        .add("statistics", statistics))

        return Ok(("analyze", updated_state))

    return create_node("transform", prep_fn, exec_fn, post_fn)


def create_analysis_node():
    """Create a node for complex analysis using functional patterns."""
    def prep_fn(ctx):
        if ctx.shared_state.contains_key("transformed_data"):
            data = ctx.shared_state.get("transformed_data")
            return Ok(data)
        else:
            return Error("No transformed data found")

    def exec_fn(transformed_data):
        """Perform analysis using functional composition."""
        try:
            # Functional analysis pipeline
            values = [item["transformed_value"] for item in transformed_data]
            analysis = {
                "mean": sum(values) / len(values) if values else 0,
                "max": max(values) if values else 0,
                "min": min(values) if values else 0,
                "variance": sum((x - sum(values)/len(values))**2 for x in values) / len(values) if values else 0
            }
            return Ok(analysis)
        except Exception as e:
            return Error(f"Analysis error: {str(e)}")

    def post_fn(ctx, prep_res, exec_res):
        """Store analysis results and complete processing."""
        updated_state = ctx.shared_state.add("analysis", exec_res)
        return Ok(("complete", updated_state))

    return create_node("analyze", prep_fn, exec_fn, post_fn)


def create_data_processing_flow():
    """Create a complete data processing flow using functional composition."""
    # Create nodes
    validation_node = create_data_validation_node()
    transformation_node = create_data_transformation_node()
    analysis_node = create_analysis_node()
    
    # Create flow with functional composition
    flow = create_flow("data_processing", "validate")
    flow = add_node(flow, validation_node)
    flow = add_node(flow, transformation_node)
    flow = add_node(flow, analysis_node)
    flow = add_transition(flow, "validate", "transform", "transform")
    flow = add_transition(flow, "transform", "analyze", "analyze")
    
    return flow


async def demonstrate_functional_data_processing():
    """Demonstrate the complete functional data processing pipeline."""
    print("=== ReactiveCodeFlow Complete Functional Example ===\n")
    
    # Sample data
    sample_data = [
        {"id": 1, "value": 25},
        {"id": 2, "value": 75},
        {"id": 3, "value": 45},
        {"id": 4, "value": 90},
        {"id": 5, "value": 15}
    ]
    
    print(f"Input data: {sample_data}")
    
    # Create the processing flow
    processing_flow = create_data_processing_flow()
    
    # Create immutable initial state
    initial_state = Map.of_list([
        ("raw_data", sample_data),
        ("validated_data", None),
        ("transformed_data", None),
        ("statistics", None),
        ("analysis", None)
    ])
    
    # Create execution context
    context = ExecutionContext(
        shared_state=initial_state,
        params=Map.empty(),
        retry_config=RetryConfig(max_retries=3, wait_seconds=0.1)
    )
    
    # Execute flow functionally
    print("\nExecuting functional data processing pipeline...")
    result = execute_flow(processing_flow, context)
    
    if result.is_ok():
        flow_result = result.ok
        
        print(f"\n✓ Processing completed successfully!")
        print(f"Execution path: {' → '.join(flow_result.execution_path)}")
        print(f"Total execution time: {flow_result.total_execution_time:.3f}s")
        
        # Display results
        if flow_result.final_state.contains_key("statistics"):
            stats = flow_result.final_state.get("statistics")
            print(f"\nStatistics: {stats}")
        
        if flow_result.final_state.contains_key("analysis"):
            analysis = flow_result.final_state.get("analysis")
            print(f"Analysis: {analysis}")
        
        if flow_result.final_state.contains_key("transformed_data"):
            transformed = flow_result.final_state.get("transformed_data")
            print(f"\nTransformed data (first 2 items): {transformed[:2]}")
        
    else:
        print(f"❌ Processing failed: {result.error}")


def demonstrate_functional_benefits():
    """Demonstrate the benefits of the functional approach."""
    print("\n=== Benefits of Functional Programming Approach ===\n")
    
    print("1. **Immutability**: All data structures are immutable")
    print("   - No accidental state mutations")
    print("   - Thread-safe by default")
    print("   - Easier to reason about")
    
    print("\n2. **Pure Functions**: No side effects")
    print("   - Predictable behavior")
    print("   - Easy to test and debug")
    print("   - Composable and reusable")
    
    print("\n3. **Railway-Oriented Programming**: Explicit error handling")
    print("   - Errors are values, not exceptions")
    print("   - Automatic error propagation")
    print("   - No hidden failure modes")
    
    print("\n4. **Functional Composition**: Build complex workflows from simple parts")
    print("   - Natural composition of functions")
    print("   - Modular and maintainable")
    print("   - Easy to extend and modify")
    
    print("\n5. **Type Safety**: Comprehensive type checking")
    print("   - Catch errors at compile time")
    print("   - Self-documenting code")
    print("   - Better IDE support")


async def main():
    """Main demonstration function."""
    await demonstrate_functional_data_processing()
    demonstrate_functional_benefits()


if __name__ == "__main__":
    asyncio.run(main())
