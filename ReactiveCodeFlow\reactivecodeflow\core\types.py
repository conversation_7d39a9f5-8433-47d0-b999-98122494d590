"""
Core types for ReactiveCodeFlow using functional programming paradigms.

This module defines immutable data structures and tagged unions using Expression's
functional programming features. All types are immutable and designed for
composition and transformation through pure functions.
"""

from dataclasses import dataclass
from typing import Any, Callable, Dict, List, Optional, TypeVar, Generic, Union, Literal
from collections.abc import Mapping, Sequence

from expression import Option, Result, Some, Nothing, Ok, Error
from expression import case, tag, tagged_union
from expression.collections import Map, Block, Seq

# Type variables for generic types
T = TypeVar('T')
E = TypeVar('E')
A = TypeVar('A')
B = TypeVar('B')

# Immutable shared state type using Expression's Map
SharedState = Map[str, Any]

# Action type for flow transitions
Action = str

# Retry configuration
@dataclass(frozen=True)
class RetryConfig:
    """Immutable retry configuration."""
    max_retries: int = 1
    wait_seconds: float = 0.0
    backoff_multiplier: float = 1.0


@dataclass(frozen=True)
class ExecutionContext:
    """Immutable execution context passed through the pipeline."""
    shared_state: SharedState
    params: Map[str, Any]
    retry_config: RetryConfig
    current_retry: int = 0


# Node execution phases as tagged union
@tagged_union
class NodePhase:
    """Tagged union representing the three phases of node execution."""
    tag: Literal["prep", "exec", "post"] = tag()
    
    prep: None = case()
    exec: None = case()
    post: None = case()


# Node execution result
@dataclass(frozen=True)
class NodeResult(Generic[T]):
    """Immutable result of node execution."""
    value: T
    action: Action
    updated_state: SharedState
    execution_time: float


# Flow execution result
@dataclass(frozen=True)
class FlowResult(Generic[T]):
    """Immutable result of flow execution."""
    final_value: T
    final_action: Action
    final_state: SharedState
    execution_path: Block[str]  # Immutable list of executed node names
    total_execution_time: float


# Node definition as immutable data structure
@dataclass(frozen=True)
class NodeDefinition(Generic[T, E]):
    """Immutable node definition using pure functions."""
    name: str
    prep_fn: Callable[[ExecutionContext], Result[Any, E]]
    exec_fn: Callable[[Any], Result[T, E]]
    post_fn: Callable[[ExecutionContext, Any, T], Result[tuple[Action, SharedState], E]]
    fallback_fn: Optional[Callable[[Any, E], Result[T, E]]] = None
    retry_config: RetryConfig = RetryConfig()


# Batch node definition
@dataclass(frozen=True)
class BatchNodeDefinition(Generic[T, E]):
    """Immutable batch node definition for processing sequences."""
    name: str
    prep_fn: Callable[[ExecutionContext], Result[Seq[Any], E]]
    exec_fn: Callable[[Any], Result[T, E]]
    post_fn: Callable[[ExecutionContext, Seq[Any], Seq[T]], Result[tuple[Action, SharedState], E]]
    fallback_fn: Optional[Callable[[Any, E], Result[T, E]]] = None
    retry_config: RetryConfig = RetryConfig()


# Flow transition as immutable data structure
@dataclass(frozen=True)
class FlowTransition:
    """Immutable flow transition definition."""
    from_node: str
    to_node: str
    action: Action = "default"


# Flow definition as immutable data structure
@dataclass(frozen=True)
class FlowDefinition:
    """Immutable flow definition."""
    name: str
    start_node: str
    nodes: Map[str, NodeDefinition]
    transitions: Block[FlowTransition]  # Immutable list of transitions
    
    def get_node(self, name: str) -> Option[NodeDefinition]:
        """Get a node by name."""
        return self.nodes.get(name)
    
    def get_next_node(self, current_node: str, action: Action) -> Option[str]:
        """Get the next node based on current node and action."""
        matching_transitions = self.transitions.filter(
            lambda t: t.from_node == current_node and t.action == action
        )
        return matching_transitions.head().map(lambda t: t.to_node)


# Batch flow definition
@dataclass(frozen=True)
class BatchFlowDefinition:
    """Immutable batch flow definition for processing multiple items."""
    name: str
    flow_definition: FlowDefinition
    batch_params_fn: Callable[[ExecutionContext], Result[Seq[Map[str, Any]], str]]


# Node state during execution
@tagged_union
class NodeState:
    """Tagged union representing node execution state."""
    tag: Literal["pending", "running", "completed", "failed"] = tag()
    
    pending: None = case()
    running: NodePhase = case()
    completed: NodeResult = case()
    failed: str = case()  # Error message


# Flow state during execution  
@tagged_union
class FlowState:
    """Tagged union representing flow execution state."""
    tag: Literal["pending", "running", "completed", "failed"] = tag()
    
    pending: None = case()
    running: str = case()  # Current node name
    completed: FlowResult = case()
    failed: str = case()  # Error message
