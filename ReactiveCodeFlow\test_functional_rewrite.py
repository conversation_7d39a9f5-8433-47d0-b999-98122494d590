"""
Comprehensive test demonstrating the functional rewrite of PocketFlow.

This test validates that ReactiveCodeFlow maintains all the functionality
of the original PocketFlow while providing the benefits of functional programming.
"""

import time
import asyncio
from typing import Any

from reactivecodeflow.core.types import ExecutionContext, RetryConfig
from reactivecodeflow.core.node import create_node, create_batch_node, execute_node, execute_batch_node
from reactivecodeflow.core.flow import create_flow, add_node, add_transition, execute_flow
from reactivecodeflow.core.async_support import create_async_node, execute_async_node
from expression import Ok, Error, Some, Nothing
from expression.collections import Map, Seq


def test_basic_node_execution():
    """Test basic node execution with prep -> exec -> post phases."""
    print("Testing basic node execution...")
    
    def create_test_node():
        def prep_fn(ctx):
            value_option = ctx.shared_state.get("input")
            if value_option.is_some():
                return Ok(value_option.value)
            else:
                return Error("No input provided")
        
        def exec_fn(input_value):
            return Ok(input_value * 2)
        
        def post_fn(ctx, prep_res, exec_res):
            updated_state = ctx.shared_state.add("output", exec_res)
            return Ok(("success", updated_state))
        
        return create_node("test", prep_fn, exec_fn, post_fn)
    
    # Create node and context
    test_node = create_test_node()
    initial_state = Map.of_list([("input", 21)])
    context = ExecutionContext(
        shared_state=initial_state,
        params=Map.empty(),
        retry_config=RetryConfig()
    )
    
    # Execute node
    result = execute_node(test_node, context)
    
    match result:
        case Ok(node_result):
            output = node_result.updated_state.get("output")
            assert output.is_some() and output.value == 42, f"Expected 42, got {output}"
            assert node_result.action == "success"
            print("✓ Basic node execution works correctly")
        case Error(error):
            raise AssertionError(f"Node execution failed: {error}")


def test_retry_mechanism():
    """Test retry mechanism with fallback."""
    print("Testing retry mechanism...")
    
    attempt_count = 0
    
    def create_retry_node():
        def prep_fn(ctx):
            return Ok("test_data")
        
        def exec_fn(data):
            nonlocal attempt_count
            attempt_count += 1
            if attempt_count < 3:
                return Error(f"Attempt {attempt_count} failed")
            return Ok("success_after_retries")
        
        def post_fn(ctx, prep_res, exec_res):
            updated_state = ctx.shared_state.add("result", exec_res)
            return Ok(("complete", updated_state))
        
        def fallback_fn(prep_res, error):
            return Ok("fallback_result")
        
        return create_node(
            "retry_test", prep_fn, exec_fn, post_fn, fallback_fn,
            RetryConfig(max_retries=3, wait_seconds=0.01)
        )
    
    retry_node = create_retry_node()
    context = ExecutionContext(
        shared_state=Map.empty(),
        params=Map.empty(),
        retry_config=RetryConfig()
    )
    
    result = execute_node(retry_node, context)
    
    match result:
        case Ok(node_result):
            output = node_result.updated_state.get("result")
            assert output.is_some() and output.value == "success_after_retries"
            assert attempt_count == 3
            print("✓ Retry mechanism works correctly")
        case Error(error):
            raise AssertionError(f"Retry test failed: {error}")


def test_batch_processing():
    """Test batch node processing."""
    print("Testing batch processing...")
    
    def create_batch_processor():
        def prep_fn(ctx):
            items_option = ctx.shared_state.get("items")
            if items_option.is_some():
                return Ok(Seq.of_iterable(items_option.value))
            else:
                return Error("No items")
        
        def exec_fn(item):
            return Ok(item * item)  # Square each item
        
        def post_fn(ctx, prep_res, exec_res_list):
            total = exec_res_list.sum()
            updated_state = ctx.shared_state.add("total", total)
            return Ok(("complete", updated_state))
        
        return create_batch_node("batch_processor", prep_fn, exec_fn, post_fn)
    
    batch_node = create_batch_processor()
    initial_state = Map.of_list([("items", [1, 2, 3, 4, 5])])
    context = ExecutionContext(
        shared_state=initial_state,
        params=Map.empty(),
        retry_config=RetryConfig()
    )
    
    result = execute_batch_node(batch_node, context)
    
    match result:
        case Ok(node_result):
            total = node_result.updated_state.get("total")
            expected = 1 + 4 + 9 + 16 + 25  # Sum of squares
            assert total.is_some() and total.value == expected, f"Expected {expected}, got {total}"
            print("✓ Batch processing works correctly")
        case Error(error):
            raise AssertionError(f"Batch processing failed: {error}")


def test_flow_orchestration():
    """Test flow orchestration with multiple nodes and transitions."""
    print("Testing flow orchestration...")
    
    def create_input_node():
        def prep_fn(ctx):
            return Ok(None)
        
        def exec_fn(_):
            return Ok("Hello")
        
        def post_fn(ctx, prep_res, exec_res):
            updated_state = ctx.shared_state.add("message", exec_res)
            return Ok(("process", updated_state))
        
        return create_node("input", prep_fn, exec_fn, post_fn)
    
    def create_process_node():
        def prep_fn(ctx):
            message_option = ctx.shared_state.get("message")
            if message_option.is_some():
                return Ok(message_option.value)
            else:
                return Error("No message")
        
        def exec_fn(message):
            return Ok(f"{message}, World!")
        
        def post_fn(ctx, prep_res, exec_res):
            updated_state = ctx.shared_state.add("final_message", exec_res)
            return Ok(("complete", updated_state))
        
        return create_node("process", prep_fn, exec_fn, post_fn)
    
    # Create flow
    input_node = create_input_node()
    process_node = create_process_node()
    
    flow = (create_flow("test_flow", "input")
            .pipe(lambda f: add_node(f, input_node))
            .pipe(lambda f: add_node(f, process_node))
            .pipe(lambda f: add_transition(f, "input", "process", "process")))
    
    # Execute flow
    context = ExecutionContext(
        shared_state=Map.empty(),
        params=Map.empty(),
        retry_config=RetryConfig()
    )
    
    result = execute_flow(flow, context)
    
    match result:
        case Ok(flow_result):
            final_message = flow_result.final_state.get("final_message")
            assert final_message.is_some() and final_message.value == "Hello, World!"
            assert flow_result.execution_path == Seq.of("input", "process")
            print("✓ Flow orchestration works correctly")
        case Error(error):
            raise AssertionError(f"Flow execution failed: {error}")


async def test_async_execution():
    """Test asynchronous node execution."""
    print("Testing async execution...")
    
    def create_async_test_node():
        async def prep_fn(ctx):
            await asyncio.sleep(0.01)  # Simulate async prep
            value_option = ctx.shared_state.get("input")
            if value_option.is_some():
                return Ok(value_option.value)
            else:
                return Error("No input")
        
        async def exec_fn(input_value):
            await asyncio.sleep(0.01)  # Simulate async work
            return Ok(f"Processed: {input_value}")
        
        async def post_fn(ctx, prep_res, exec_res):
            await asyncio.sleep(0.01)  # Simulate async post
            updated_state = ctx.shared_state.add("output", exec_res)
            return Ok(("complete", updated_state))
        
        return create_async_node("async_test", prep_fn, exec_fn, post_fn)
    
    async_node = create_async_test_node()
    initial_state = Map.of_list([("input", "test_data")])
    context = ExecutionContext(
        shared_state=initial_state,
        params=Map.empty(),
        retry_config=RetryConfig()
    )
    
    result = await execute_async_node(async_node, context)
    
    match result:
        case Ok(node_result):
            output = node_result.updated_state.get("output")
            assert output.is_some() and output.value == "Processed: test_data"
            print("✓ Async execution works correctly")
        case Error(error):
            raise AssertionError(f"Async execution failed: {error}")


def test_immutability():
    """Test that all operations maintain immutability."""
    print("Testing immutability...")
    
    # Create initial state
    initial_state = Map.of_list([("counter", 0)])
    
    # Create a node that "modifies" state
    def create_counter_node():
        def prep_fn(ctx):
            counter_option = ctx.shared_state.get("counter")
            if counter_option.is_some():
                return Ok(counter_option.value)
            else:
                return Error("No counter")
        
        def exec_fn(counter):
            return Ok(counter + 1)
        
        def post_fn(ctx, prep_res, exec_res):
            updated_state = ctx.shared_state.add("counter", exec_res)
            return Ok(("complete", updated_state))
        
        return create_node("counter", prep_fn, exec_fn, post_fn)
    
    counter_node = create_counter_node()
    context = ExecutionContext(
        shared_state=initial_state,
        params=Map.empty(),
        retry_config=RetryConfig()
    )
    
    # Execute node
    result = execute_node(counter_node, context)
    
    match result:
        case Ok(node_result):
            # Original state should be unchanged
            original_counter = initial_state.get("counter")
            assert original_counter.is_some() and original_counter.value == 0, "Original state was mutated!"

            # New state should have updated value
            new_counter = node_result.updated_state.get("counter")
            assert new_counter.is_some() and new_counter.value == 1, "New state not updated correctly"

            print("✓ Immutability maintained correctly")
        case Error(error):
            raise AssertionError(f"Immutability test failed: {error}")


def run_all_tests():
    """Run all tests to validate the functional rewrite."""
    print("=== ReactiveCodeFlow Functional Rewrite Tests ===\n")
    
    try:
        test_basic_node_execution()
        test_retry_mechanism()
        test_batch_processing()
        test_flow_orchestration()
        test_immutability()
        
        # Run async test
        asyncio.run(test_async_execution())
        
        print("\n🎉 All tests passed! ReactiveCodeFlow functional rewrite is working correctly.")
        print("\nKey achievements:")
        print("✓ Pure functional node execution")
        print("✓ Immutable state management")
        print("✓ Railway-oriented error handling")
        print("✓ Functional flow orchestration")
        print("✓ Batch processing with functional composition")
        print("✓ Async support with functional effects")
        print("✓ Complete immutability throughout")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        raise


if __name__ == "__main__":
    run_all_tests()
