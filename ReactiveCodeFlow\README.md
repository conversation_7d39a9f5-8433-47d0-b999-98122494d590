# ReactiveCodeFlow

A production-quality functional programming rewrite of the PocketFlow Core Framework using the Expression library.

## Overview

ReactiveCodeFlow transforms the object-oriented PocketFlow framework into a purely functional architecture, leveraging the Expression library's powerful functional programming constructs. This rewrite maintains all original functionality while providing the benefits of immutability, composability, and railway-oriented programming.

## Key Features

### Functional Programming Paradigms
- **Pure Functions Only**: No classes, inheritance, or object methods
- **Immutable Data Structures**: All state is immutable using Expression's collections
- **Railway-Oriented Programming**: Automatic error handling with Option/Result types
- **Compositional Design**: Build complex workflows through function composition
- **Type Safety**: Comprehensive type hints with Expression's type system

### Core Capabilities
- **Node Execution**: Three-phase execution (prep → exec → post) using pure functions
- **Flow Orchestration**: Immutable flow definitions with action-based transitions
- **Batch Processing**: Functional batch operations with parallel execution support
- **Async Support**: Full asynchronous execution with Expression's async effects
- **Error Handling**: Retry logic, fallbacks, and graceful degradation
- **State Management**: Immutable shared state using Expression's Map type

## Architecture Transformation

### From OOP to Functional

**Original PocketFlow (OOP)**:
```python
class Node:
    def __init__(self, max_retries=1):
        self.max_retries = max_retries

    def prep(self, shared): pass
    def exec(self, prep_res): pass
    def post(self, shared, prep_res, exec_res): pass
```

**ReactiveCodeFlow (Functional)**:
```python
@dataclass(frozen=True)
class NodeDefinition:
    name: str
    prep_fn: Callable[[ExecutionContext], Result[Any, E]]
    exec_fn: Callable[[Any], Result[T, E]]
    post_fn: Callable[[ExecutionContext, Any, T], Result[tuple[Action, SharedState], E]]
    retry_config: RetryConfig = RetryConfig()
```

### Benefits of Functional Approach

1. **Immutability**: No side effects, easier reasoning about code
2. **Composability**: Functions compose naturally for complex workflows
3. **Testability**: Pure functions are easier to test and debug
4. **Concurrency**: Immutable data structures are thread-safe by default
5. **Error Handling**: Railway-oriented programming prevents error propagation
6. **Type Safety**: Expression's type system catches errors at compile time

## Quick Start

### Installation

```bash
uv venv ReactiveCodeFlow
source ReactiveCodeFlow/bin/activate  # On Windows: ReactiveCodeFlow\Scripts\activate
uv add expression
```

### Basic Example

```python
from reactivecodeflow import create_node, create_flow, execute_flow
from reactivecodeflow.core.types import ExecutionContext, RetryConfig
from expression import Ok, Error
from expression.collections import Map

# Create a simple node
def create_hello_node():
    def prep_fn(ctx):
        return Ok(ctx.shared_state.get("name").value_or("World"))

    def exec_fn(name):
        return Ok(f"Hello, {name}!")

    def post_fn(ctx, prep_res, exec_res):
        updated_state = ctx.shared_state.add("greeting", exec_res)
        return Ok(("default", updated_state))

    return create_node("hello", prep_fn, exec_fn, post_fn)

# Create and execute flow
hello_node = create_hello_node()
flow = create_flow("greeting_flow", "hello").pipe(
    lambda f: add_node(f, hello_node)
)

# Execute with initial context
initial_state = Map.of_dict({"name": "ReactiveCodeFlow"})
context = ExecutionContext(
    shared_state=initial_state,
    params=Map.empty(),
    retry_config=RetryConfig()
)

result = execute_flow(flow, context)
print(result.final_state.get("greeting"))  # "Hello, ReactiveCodeFlow!"
```

## Documentation

### Core Concepts

1. **[Types](docs/types.md)**: Immutable data structures and tagged unions
2. **[Nodes](docs/nodes.md)**: Pure functional node execution
3. **[Flows](docs/flows.md)**: Immutable flow orchestration
4. **[Effects](docs/effects.md)**: Computational expressions for workflows
5. **[Async Support](docs/async.md)**: Asynchronous execution patterns

### Migration Guide

See [MIGRATION.md](docs/MIGRATION.md) for detailed guidance on migrating from PocketFlow to ReactiveCodeFlow.

## Examples

The `examples/` directory contains comprehensive examples demonstrating:

- Basic node and flow creation
- Batch processing patterns
- Async workflow execution
- Error handling strategies
- Complex flow composition
- Migration from PocketFlow patterns

## Contributing

1. Follow functional programming principles strictly
2. Use Expression library constructs for all operations
3. Maintain immutability throughout
4. Provide comprehensive type hints
5. Include tests for all functionality

## License

MIT License - see LICENSE file for details.